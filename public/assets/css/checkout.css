.animation-dots {
    margin-top: 40px;
    overflow: hidden;
    display: flex;
    justify-content: center;
}
.tax-banner {
    padding-top: 100px;
    padding-bottom: 50px;
    background-repeat: no-repeat;
    background-size: cover;
}
.main-title {
    color: #fbc30b;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter;
    font-size: 44px;
    font-style: normal;
    font-weight: 900;
    line-height: 42px; /* 95.455% */
    text-transform: uppercase;
}

.subtitle {
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    margin-top: 15px;
    margin-bottom: 40px;
}
.promo-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin: 40px auto;
    max-width: 800px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
}

.promo-card h3 {
    text-align: center;
    color: #ff6b35;
    font-size: 25px;
    font-style: normal;
    font-weight: 700;
    line-height: 40px; /* 125% */
    background: var(
        --gradient2,
        linear-gradient(270deg, #eb2805 0%, #fb8f0b 100%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.promo-card p {
    color: #1a1d24;
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px; /* 130% */
    margin-top: 10px;
}

.rocket-icon {
    position: absolute;
    width: 150px !important;
    height: auto;
    top: -80px;
    left: -46px;
}
.slots-section {
    text-align: center;
    margin-top: 60px;
    position: relative;
    z-index: 2;
}

/* Custom gradient for the progress bar to match the image */
.progress-gradient {
    background: linear-gradient(
        90deg,
        #f9d423 0%,
        #ff8c1a 35%,
        #f73772 70%,
        #c832e3 100%
    );
    height: 20px;
}
.gift-icon {
    width: 81px;
    max-height: 96px;
    transform: translateY(-50%) !important;
    position: absolute;
    right: -60px;
    top: 0;
}

/* Subtle background pattern for the banner */
.banner-bg-pattern {
    background-color: #10289a; /* Deep blue from the image */
    position: relative;
    overflow: hidden;
}

.banner-bg-pattern::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(
            rgba(255, 255, 255, 0.02) 1px,
            transparent 1px
        ),
        linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: 0;
}

/* Sparkle animation for the gift box */
@keyframes sparkle {
    0%,
    100% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1);
        opacity: 1;
    }
}

.sparkle {
    position: absolute;
    background-color: #ffd700;
    border-radius: 50%;
    animation: sparkle 1.8s infinite ease-in-out;
    box-shadow: 0 0 6px #ffd700, 0 0 12px #ffd700;
}
.slots-title {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    color: #fff;
}

.slots-subtitle {
    font-size: 16px;
    margin-bottom: 35px;
    /* line-height: 1.6; */
    color: #fff;
}

.progress-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.custom-progress {
    height: 30px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
}
.progress-container {
    max-width: 450px;
}
.progress-bar-custom {
    height: 25px !important;
    padding: 4px !important;
    border-radius: 20px !important;
}
.progress-bar {
    border-radius: 20px;
    background: linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.15) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(255, 255, 255, 0.15) 75%,
            transparent 75%,
            transparent
        ),
        linear-gradient(90deg, #ffae00 0%, #e200f8 98.72%) !important;
    background-size: 20px 20px, 100% 100% !important;
}

.dots-pattern {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.dot-group {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
}

.dot {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
}
.animation-dots > img {
    animation: dots 0.7s linear 0.2s infinite;
}
@keyframes dots {
    0% {
        opacity: 0.4;
        transform: translateY(-100%);
    }
    to {
        opacity: 0.9;
        transform: translateY(100%);
    }
}

@media (max-width: 768px) {
    .main-title {
        font-size: 2.5rem;
    }

    .promo-card {
        margin: 20px;
        padding: 20px;
    }

    .promo-card h3 {
        font-size: 1.8rem;
    }

    .rocket-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .slots-title {
        font-size: 2rem;
    }
}

.floating-animation {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
.animate-bounce {
    animation: bounce 1s infinite;
}
@keyframes bounce {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}
.arrow-bounce {
    width: 162px;
    height: 44px;
    animation: arrowBounce 2s ease-in-out infinite;
}

@keyframes arrowBounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(-50%) translateY(-50%);
    }
    40% {
        transform: translateX(-50%) translateY(-70%);
    }
    60% {
        transform: translateX(-50%) translateY(-60%);
    }
}

.arrow-container {
    position: relative;
    margin: 20px 0;
}
.course-detail-title {
    font-size: 32px;
    font-style: normal;
    font-weight: 900;
    text-transform: uppercase;
    background: linear-gradient(270deg, #0020ac 0%, #1540ff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 50px;
}
.course-detail-desc {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    color: #333;
}
.module-count {
    border-radius: 19.081px;
    border: 1.193px solid #ff2a00;
    background: var(
        --gradient2,
        linear-gradient(270deg, #eb2805 0%, #fb8f0b 100%)
    );
    padding: 5px 12px;
    color: #fff;
    display: flex;
}

/* Section Buy Styles */
.section-buy {
    padding: 80px 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.buy-content {
    color: white;
    padding: 20px 30px;
    border-radius: 15px;
    border: 2px solid #fff;
    background: linear-gradient(180deg, #a600f8 0%, #0020ac 100%);
    position: relative;
}
.special-tag {
    position: absolute;
    top: -40px;
    left: -38px;
    width: 153px;
}

.price-section {
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.original-price {
    font-size: 24px;
    text-decoration: line-through;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
}

.current-price {
    font-size: 42px;
    font-weight: 900;
    color: #fbc30b;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    gap: 10px;
}

.benefit-item:last-child {
    border-bottom: none;
}

.benefit-icon {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
    margin-top: 2px;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}
.buy-title {
    font-size: 20px;
    text-align: center;
    font-weight: 600;
    margin-bottom: 20px;
}

.highlight-text {
    color: #fff;
    font-size: 16px;
}

.cta-section {
    text-align: center;
}

.btn-buy-now {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border: none;
    padding: 20px 40px;
    border-radius: 50px;
    color: white;
    font-weight: 900;
    font-size: 18px;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(255, 107, 53, 0.4);
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
    min-width: 280px;
}

.btn-buy-now:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(255, 107, 53, 0.5);
}

.btn-buy-now:active {
    transform: translateY(-1px);
}

.btn-text {
    display: block;
    font-size: 20px;
    margin-bottom: 5px;
}

.btn-subtext {
    display: block;
    font-size: 12px;
    opacity: 0.9;
    text-transform: none;
    letter-spacing: 0;
}

.guarantee-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 15px;
}

.course-preview {
    border: 2px solid #fff;
    margin-top: 25px;
    border-radius: 20px;
}

.course-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.course-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 60px;
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.course-image-container:hover .play-overlay {
    opacity: 1;
}

.course-image-container:hover .course-image {
    transform: scale(1.05);
}

.play-button {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 30px;
    margin-bottom: 15px;
    box-shadow: 0 8px 30px rgba(255, 107, 53, 0.4);
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

.preview-text {
    color: white;
    font-weight: 600;
    font-size: 16px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.course-stats {
    display: flex;
    justify-content: space-around;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 900;
    color: #fbc30b;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 1px;
}
.b-box {
    border-radius: 4.588px;
    background: var(
        --gradient2,
        linear-gradient(0deg, #ffc448 0%, #ffec95 100%)
    );
    display: inline-block;
    width: fit-content;
    color: #0020ac !important;
    padding: 2px 12px;
    margin-bottom: 8px;
}

.modal-regis .close-btn {
    position: absolute;
    top: 6px;
    right: 10px;
    background: none;
    border: none;
    font-size: 25px;
    cursor: pointer;
    background: #fff;
    /* padding: 5px; */
    /* border: 1px solid #DC3F2E; */
    border-radius: 50%;
    color: #dc3f2e !important;
    font-weight: normal !important;
    width: 30px;
    z-index: 99999;
    height: 30px;
}

.modal-regis .nav-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.modal-regis .nav-btn {
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
}

.modal-regis .prev-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    color: #666;
}

.modal-regis .next-btn {
    background-color: #ffa726;
    border: none;
    color: white;
}

.modal-regis .tab-content {
    display: none;
}

.modal-regis .tab-content.active {
    display: block;
}

.modal-regis .user-info-container {
    background-color: white;
    padding: 20px;
    border-radius: 15px;
    border: 1px solid #ffd8a8;
    background: linear-gradient(to right, #fffcf5, #fff5eb);
    border-radius: 20px;
    border: 2px solid var(--gradient-2, #eb2805);
    background: #fff;
    box-shadow: 0px 7px 35px 0px rgba(0, 0, 0, 0.15);
}

.user-info-container h6 {
    font-size: 20px;
    color: #333;
}

.modal-regis .user-info-container a {
    color: #fa8128;
    text-decoration: none;
}

.modal-regis .success-container {
    background-color: white;
    padding: 40px 20px;
    border-radius: 15px;
    text-align: center;
    /* background: linear-gradient(to right, #fffaf0, #fff5eb);
           */
}

.modal-regis .success-icon {
    width: 65px;
    height: 65px;
    background-color: #4caf50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: white;
    font-size: 40px;
}

.modal-regis .success-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
}

.modal-regis .success-text {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.modal-regis .start-course-btn {
    background: linear-gradient(to right, #ff8a00, #ff5722);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: bold;
    font-size: 16px;
    text-transform: uppercase;
    box-shadow: 0 4px 10px rgba(255, 138, 0, 0.2);
    width: 240px;
}

.modal-regis .contact-text {
    color: #666;
    margin-top: 20px;
}

.modal-regis .hotline {
    color: #ff5722;
    font-weight: bold;
}

.modal-regis .coupon-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    margin-bottom: 15px;
}

.modal-regis .coupon-btn {
    background-color: white;
    border: 1px dashed #ff5722;
    border-radius: 30px;
    padding: 6px 15px;
    color: #ff5722;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s;
}

.modal-regis .coupon-btn:hover {
    background-color: #fff5f0;
    transform: translateY(-2px);
}

.modal-regis .coupon-tag {
    font-size: 10px;
    color: white;
    background-color: #ff5722;
    padding: 2px 4px;
    border-radius: 2px;
    margin-right: 5px;
}
.regis-content {
    padding: 20px;
    border-radius: 15px;
    background: #fff;
    box-shadow: 0px 4.231px 15.44px 0px rgba(0, 0, 0, 0.15);
}
.modal-regis #btn-checkout-regis {
    border-radius: 10px;
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    border: none;
    display: block;
    width: 100%;
    height: 56px;
    font-size: 18px;
    text-transform: uppercase;
    font-weight: bold;
    color: #fff;
}

.form-label {
    margin-bottom: 0.25rem;
    font-size: 16px;
    position: absolute;
    background: transparent;
    top: -12px;
    z-index: 10;
    left: 14px;
    padding: 0 3px;
    color: #333;
}

.modal-auth .form-label {
    margin-bottom: 0.25rem;
    font-size: 16px;
    position: absolute;
    background: transparent;
    top: -12px;
    z-index: 10;
    left: 14px;
    padding: 0 3px;
    color: #333;
}

.modal-regis .phone-group button {
    border: none;
    border-right: 1px solid #dadada;
    border-radius: 0;
    display: flex;
    width: 110px;
    font-size: 16px;
    align-items: center;
    gap: 6px;
    justify-content: center;
}

.modal-auth .form-control {
    border-radius: 10px;
    padding: 0.75rem;
    border-radius: 5px;
    border: 1px solid #dadada;
    height: 52px;
}
.form-wrap {
    position: relative;
}

.modal-auth .form-wrap {
    position: relative;
}
.modal-regis .price-info {
    display: flex;
    justify-content: space-between;
    color: #333;
}
.modal-regis input#couponInput {
    color: #dc3f2e;
    padding: 6px 10px;
}

.modal-regis button#applyCoupon {
    border-radius: 5px;
    background: #fa8128;
    width: 110px;
    font-size: 16px;
}
.modal-regis .total-price {
    display: flex;
    justify-content: space-between;
    padding: 15px 0px;
    color: #333;
}

.modal-regis .price {
    color: rgb(255, 87, 34);
    font-weight: bold;
}

/* Hot Deal Styles - BEM Refactor */
.hot-deal {
    background: linear-gradient(180deg, #ffdd81 0%, #ffc940 100%);
    border-radius: 45px;
    padding: 30px 16px;
    margin-top: 30px;
    position: relative;
    text-align: center;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
}

.hot-deal__original-price {
    position: absolute;
    top: -22px;
    left: 23%;
    transform: translateX(-50%);
    background: #0240b6;
    color: white;
    font-weight: 700;
    font-size: 20px;
    padding: 10px 30px;
    border-radius: 20px;
    box-shadow: 0 4px 10px rgba(0, 32, 172, 0.4);
    text-decoration: line-through;
}
.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgb(102, 102, 102);
    cursor: pointer;
    background: none;
    border-width: initial;
    border-style: none;
    border-color: initial;
    border-image: initial;
}
.hot-deal__promo {
    text-transform: uppercase;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.hot-deal__promo-label {
    font-size: 24px;
    color: #1e293b;
    font-weight: 600;
}

.hot-deal__promo-price {
    font-size: 38px;
    color: #0020ac;
    font-weight: 900;
    font-style: italic;
}

.hot-deal__promo-icon {
    width: 116px;
    height: auto;
    margin-left: 5px;
    position: absolute;
    top: -34px;
    right: -30px;
    max-width: inherit !important;
    max-height: inherit !important;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .hot-deal {
        padding: 30px 15px 15px;
        border-radius: 30px;
    }

    .hot-deal__original-price {
        font-size: 16px;
        padding: 8px 20px;
        top: -18px;
    }

    .hot-deal__promo {
        gap: 8px;
        flex-wrap: wrap; /* Allow wrapping on small screens */
    }

    .hot-deal__promo-price {
        font-size: 32px;
    }

    .hot-deal__promo-icon {
        width: 55px;
    }
}
.modal-regis .phone-group {
    border-width: 1px;
    border-style: solid;
    border-color: rgb(218, 218, 218);
    border-image: initial;
    border-radius: 5px;
}
.modal-regis .phone-group input {
    border-width: initial;
    border-style: none;
    border-color: initial;
    border-image: initial;
}
@media (max-width: 768px) {
    .section-buy {
        padding: 60px 0;
    }

    .buy-title {
        font-size: 28px;
    }

    .current-price {
        font-size: 32px;
    }

    .btn-buy-now {
        padding: 15px 30px;
        font-size: 16px;
        min-width: 250px;
    }

    .course-image,
    .course-placeholder {
        height: 250px;
    }

    .course-stats {
        flex-direction: column;
        gap: 15px;
    }

    .benefit-item {
        flex-direction: column;
        gap: 8px;
    }
}

@media (max-width: 556px) {
    .tax-banner {
        padding: 30px 0;
    }

    .main-title {
        font-size: 20px;
    }

    .subtitle {
        font-size: 16px;
    }

    img.rocket-icon {
        width: 120px !important;
        height: auto;
    }

    .promo-card h3 {
        line-height: 26px;
        font-size: 16px;
    }

    .promo-card p {
        font-size: 16px;
    }

    p.slots-subtitle {
        font-size: 17px;
    }

    .progress-container {
        max-width: 290px;
    }

    .buy-content {
        padding: 15px;
        padding-top: 40px;
    }

    h5.buy-title {
        font-size: 18px;
    }

    img.special-tag {
        width: 100px;
        top: -28px;
        left: -27px;
    }

    .regis-content {
        margin-top: 30px;
    }

    .regis-content {
        padding: 10px;
    }

    .module-count {
        font-size: pa;
        /* padding: 0; */
    }

    .course-detail-desc {
        flex-direction: column;
    }
}
